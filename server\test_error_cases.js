async function testErrorCases() {
    const baseUrl = 'http://localhost:9001/api/device';
    
    console.log('=== Testing Error Cases ===\n');
    
    try {
        // Test 1: Missing MAC address header for /ls
        console.log('1. Testing /ls without MAC address header...');
        const lsResponse1 = await fetch(`${baseUrl}/ls`);
        console.log('Status:', lsResponse1.status);
        if (!lsResponse1.ok) {
            const error1 = await lsResponse1.json();
            console.log('✓ Expected error:', error1);
        }
        
        // Test 2: Missing MAC address header for /upload
        console.log('\n2. Testing /upload without MAC address header...');
        const uploadResponse1 = await fetch(`${baseUrl}/upload?path=test.json&timestamp=1704067200`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/gzip'
            },
            body: Buffer.from('test')
        });
        console.log('Status:', uploadResponse1.status);
        if (!uploadResponse1.ok) {
            const error2 = await uploadResponse1.json();
            console.log('✓ Expected error:', error2);
        }
        
        // Test 3: Missing path parameter
        console.log('\n3. Testing /upload without path parameter...');
        const uploadResponse2 = await fetch(`${baseUrl}/upload?timestamp=1704067200`, {
            method: 'POST',
            headers: {
                'X-MAC-Address': '00:11:22:33:44:55',
                'Content-Type': 'application/gzip'
            },
            body: Buffer.from('test')
        });
        console.log('Status:', uploadResponse2.status);
        if (!uploadResponse2.ok) {
            const error3 = await uploadResponse2.json();
            console.log('✓ Expected error:', error3);
        }
        
        // Test 4: Missing timestamp parameter
        console.log('\n4. Testing /upload without timestamp parameter...');
        const uploadResponse3 = await fetch(`${baseUrl}/upload?path=test.json`, {
            method: 'POST',
            headers: {
                'X-MAC-Address': '00:11:22:33:44:55',
                'Content-Type': 'application/gzip'
            },
            body: Buffer.from('test')
        });
        console.log('Status:', uploadResponse3.status);
        if (!uploadResponse3.ok) {
            const error4 = await uploadResponse3.json();
            console.log('✓ Expected error:', error4);
        }
        
        // Test 5: Invalid timestamp parameter
        console.log('\n5. Testing /upload with invalid timestamp parameter...');
        const uploadResponse4 = await fetch(`${baseUrl}/upload?path=test.json&timestamp=invalid`, {
            method: 'POST',
            headers: {
                'X-MAC-Address': '00:11:22:33:44:55',
                'Content-Type': 'application/gzip'
            },
            body: Buffer.from('test')
        });
        console.log('Status:', uploadResponse4.status);
        if (!uploadResponse4.ok) {
            const error5 = await uploadResponse4.json();
            console.log('✓ Expected error:', error5);
        }
        
        console.log('\n=== Error case tests completed ===');
        
    } catch (error) {
        console.error('Error during testing:', error);
    }
}

// Run tests
testErrorCases();
