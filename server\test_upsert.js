import fs from 'fs-extra';

async function testUpsert() {
    const baseUrl = 'http://localhost:9001/api/device';
    const macAddress = '00:11:22:33:44:55';
    
    console.log('=== Testing Upsert Functionality ===\n');
    
    try {
        // Read the test file
        const gzippedContent = await fs.readFile('./test_data/test_config.json.gz');
        
        // Test 1: Check current files
        console.log('1. Current files for device:');
        const lsResponse1 = await fetch(`${baseUrl}/ls`, {
            headers: {
                'X-MAC-Address': macAddress
            }
        });
        
        if (lsResponse1.ok) {
            const files1 = await lsResponse1.json();
            console.log(files1);
            
            // Find the config/settings.json file
            const configFile = files1.find(f => f.path === 'config/settings.json');
            if (configFile) {
                console.log('Current timestamp for config/settings.json:', configFile.timestamp);
            }
        }
        
        // Test 2: Upload the same file with a new timestamp (should update existing record)
        console.log('\n2. Uploading same file with new timestamp...');
        const newTimestamp = 1704080000; // Different timestamp
        
        const uploadResponse = await fetch(`${baseUrl}/upload?path=config/settings.json&timestamp=${newTimestamp}`, {
            method: 'POST',
            headers: {
                'X-MAC-Address': macAddress,
                'Content-Type': 'application/gzip'
            },
            body: gzippedContent
        });
        
        if (uploadResponse.ok) {
            const uploadResult = await uploadResponse.json();
            console.log('✓ Upload response:', uploadResult);
        } else {
            console.log('✗ Upload failed:', uploadResponse.status, await uploadResponse.text());
        }
        
        // Test 3: Check files again (should show updated timestamp)
        console.log('\n3. Files after upsert:');
        const lsResponse2 = await fetch(`${baseUrl}/ls`, {
            headers: {
                'X-MAC-Address': macAddress
            }
        });
        
        if (lsResponse2.ok) {
            const files2 = await lsResponse2.json();
            console.log(files2);
            
            // Find the config/settings.json file
            const configFile2 = files2.find(f => f.path === 'config/settings.json');
            if (configFile2) {
                console.log('Updated timestamp for config/settings.json:', configFile2.timestamp);
                if (configFile2.timestamp === newTimestamp) {
                    console.log('✓ Upsert successful - timestamp updated');
                } else {
                    console.log('✗ Upsert failed - timestamp not updated');
                }
            }
            
            // Check that we still have the same number of files (no duplicates)
            const configFiles = files2.filter(f => f.path === 'config/settings.json');
            if (configFiles.length === 1) {
                console.log('✓ No duplicate records created');
            } else {
                console.log('✗ Duplicate records found:', configFiles.length);
            }
        }
        
        console.log('\n=== Upsert test completed ===');
        
    } catch (error) {
        console.error('Error during testing:', error);
    }
}

// Run tests
testUpsert();
