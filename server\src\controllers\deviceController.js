import { validateHandshake } from '../utils/handshakeValidation.js';
import { DtypeMapper } from '../utils/dtypeMapper.js';
import logger, { logDeviceData, logDeviceError, logDeviceResponse } from '../utils/logger.js';

export class DeviceController {
    constructor(deviceService, dataService, timeService) {
        this.deviceService = deviceService;
        this.dataService = dataService;
        this.timeService = timeService;
    }
    async handleHandshake(request, reply) {
        try {
            logger.info('Received handshake request', {
                mac: request.body.mac,
                model: request.body.model
            });

            const { mac, nchan, model, registers, trends, label, description, longitude, latitude } = request.body;

            // Validate request
            const validationError = validateHandshake(mac, nchan, model, registers);
            if (validationError) {
                logger.warn('Handshake validation error', {
                    mac,
                    error: validationError
                });
                return reply.status(400).send(validationError);
            }

            // Create metadata object with all available fields
            const metadata = {
                model: model || 'unknown',
                registers: registers || []
            };

            // Add optional fields if they exist
            if (trends) metadata.trends = trends;
            if (label) metadata.label = label;
            if (description) metadata.description = description;
            if (longitude) metadata.longitude = longitude;
            if (latitude) metadata.latitude = latitude;

            // Calculate nchan from registers array if not provided
            if (nchan !== undefined) {
                metadata.nchan = nchan;
            } else if (metadata.registers.length > 0) {
                metadata.nchan = metadata.registers.length;
                logger.info(`Calculated nchan from registers array`, {
                    mac,
                    nchan: metadata.nchan
                });
            } else {
                metadata.nchan = 0;
                logger.info('No registers provided, setting nchan to 0', { mac });
            }

            const serverTime = this.timeService.getCurrentServerTime();
            logger.info(`Server time for handshake`, { mac, serverTime });

            try {
                // Check if device already exists
                logger.info(`Looking for device with MAC: ${mac}`);
                const device = await this.deviceService.findDeviceByMac(mac);

                if (device) {
                    logger.info(`Device found during handshake`, {
                        mac,
                        deviceId: device.id
                    });

                    // Existing device flow...
                    logger.info('Handling metadata update during handshake', { mac });
                    const changes = await this.deviceService.handleMetadataUpdate(mac, metadata, this.dataService);
                    logger.info(`Metadata changes processed`, {
                        mac,
                        changeType: changes.type
                    });

                    if (changes.type === 'INCOMPATIBLE' && !changes.migrationNeeded) {
                        logger.warn('Incompatible metadata changes detected that cannot be migrated', {
                            mac,
                            changes
                        });
                        return reply.status(409).send({
                            error: 'Incompatible metadata changes',
                            changes
                        });
                    }

                    logger.info('Getting latest data timestamp for handshake response', { mac });
                    const dataTime = await this.dataService.getLatestDataTimestamp(mac);
                    logger.info(`Latest data timestamp retrieved`, {
                        mac,
                        dataTime
                    });

                    const response = `${serverTime},${dataTime},0`;
                    logger.info(`Sending handshake response for existing device`, {
                        mac,
                        response
                    });
                    return reply.status(200).send(response);
                } else {
                    logger.info('Device not found, creating new device', { mac });

                    // New device flow...
                    try {
                        logger.info(`Creating device with MAC: ${mac}`);
                        // Extract additional fields to pass directly
                        const additionalFields = {};
                        const allowedFields = ['label', 'description', 'longitude', 'latitude'];
                        for (const field of allowedFields) {
                            if (request.body[field] !== undefined) {
                                additionalFields[field] = request.body[field];
                            }
                        }

                        await this.deviceService.createDevice(mac, metadata, additionalFields);

                        logger.info('Device created, ensuring data table exists', { mac });
                        await this.dataService.ensureDataTableExists(mac, metadata);

                        const response = `${serverTime},0,0`;
                        logger.info(`Sending handshake response for new device`, {
                            mac,
                            response
                        });
                        return reply.status(201).send(response);
                    } catch (createError) {
                        logDeviceError(mac, null, 'Error creating device', createError);
                        throw createError;
                    }
                }
            } catch (error) {
                logDeviceError(mac, null, 'Handshake processing error', error);

                if (error.message.includes('already exists')) {
                    return reply.status(409).send({
                        error: 'Device already registered',
                        message: error.message
                    });
                }
                throw error;
            }
        } catch (error) {
            logDeviceError(
                request.body.mac || 'unknown',
                null,
                'Handshake error',
                error
            );

            return reply.status(500).send({
                error: 'Failed to process device handshake',
                message: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }
    async handleDevicePost(request, reply) {
        try {
            logger.info('Received device post request');

            const mac = request.headers['mac'];
            const devTime = request.headers['dev-time'];
            const payload = request.body;

            // Log the request with MAC and device time
            logDeviceData(mac, devTime, 'Device data post received', {
                headers: request.headers,
                contentType: request.headers['content-type']
            });

            if (!mac) {
                logger.warn('Missing Mac header in request');
                return reply.code(400).send({ error: 'Missing Mac header' });
            }

            logger.info(`Processing request for device: ${mac}`);
            const serverTime = this.timeService.getCurrentServerTime();
            logger.info(`Server time: ${serverTime}`, { mac, deviceTime: devTime });

            logger.info(`Looking for device with MAC: ${mac}`);
            const device = await this.deviceService.findDeviceByMac(mac);

            if (!device) {
                logger.warn(`Device not found: ${mac}`);
                return reply.code(404).send({ error: 'Device not registered' });
            }

            logger.info(`Device found`, { mac, deviceId: device.id });

            // If Dev-Time header is present, it's a data payload
            if (devTime) {
                logger.info(`Processing data payload`, { mac, deviceTime: devTime });

                // Get metadata from either meta_history or meta field (preferred)
                let metadata;
                if (device.meta) {
                    logger.debug('Using metadata from meta field', { mac });
                    metadata = JSON.parse(device.meta);
                } else if (device.meta_history) {
                    logger.debug('Using metadata from meta_history', { mac });
                    metadata = JSON.parse(device.meta_history)[device.meta_version - 1].metadata;
                } else {
                    logDeviceError(mac, devTime, 'No metadata found for device');
                    return reply.code(500).send({ error: 'Device metadata not found' });
                }

                logger.debug(`Device metadata retrieved`, { mac, metaVersion: device.meta_version });

                const contentType = request.headers['content-type'] || 'application/json';
                const format = contentType.includes('csv') ? 'CSV' : 'JSON';

                logger.info(`Parsing payload as ${format}`, { mac, deviceTime: devTime, contentType });

                const parsedPayload = this.dataService.parseDataPayload(payload, format);

                // Log a summary of the payload rather than the full payload
                const payloadSummary = Array.isArray(parsedPayload)
                    ? { rowCount: parsedPayload.length }
                    : { keys: Object.keys(parsedPayload) };

                logger.info(`Payload parsed successfully`, {
                    mac,
                    deviceTime: devTime,
                    format,
                    ...payloadSummary
                });

                logger.info('Validating payload', { mac, deviceTime: devTime });
                const validation = this.dataService.validateDataPayload(parsedPayload, metadata);

                // We'll continue processing even if there are validation errors
                // The dataService.logData method will handle skipping invalid datapoints
                if (!validation.valid) {
                    logger.warn(`Payload validation has errors`, {
                        mac,
                        deviceTime: devTime,
                        errors: validation.errors
                    });
                    logger.info('Continuing with processing - invalid datapoints will be skipped', { mac });
                } else {
                    logger.info('Payload validation successful', { mac, deviceTime: devTime });
                }

                // Check if payload is in the new format (array of arrays)
                if (Array.isArray(parsedPayload) && parsedPayload.length > 0 && Array.isArray(parsedPayload[0])) {
                    logger.info('Processing nested array format data', {
                        mac,
                        deviceTime: devTime,
                        rowCount: parsedPayload.length
                    });

                    // For nested array format, we pass the data directly to logData
                    // Each row already contains its own timestamp as the first element
                    logger.info(`Logging ${parsedPayload.length} rows of data for device ${mac}`, {
                        mac,
                        deviceTime: devTime,
                        format: 'nested-array'
                    });

                    // Extract the latest timestamp from the payload for logging
                    let latestTimestamp = 0;
                    if (parsedPayload.length > 0 && parsedPayload[0].length > 0) {
                        latestTimestamp = Math.max(...parsedPayload.map(row => row[0] || 0));
                    }

                    await this.dataService.logData(mac, parseInt(devTime), parsedPayload);

                    // Log the latest timestamp from the payload
                    logDeviceData(mac, devTime, 'Data logged successfully', {
                        rowCount: parsedPayload.length,
                        latestTimestamp
                    });
                } else {
                    // Legacy format (object with key-value pairs)
                    logger.info('Formatting legacy payload for database', { mac, deviceTime: devTime });
                    const formattedPayload = Object.entries(parsedPayload).reduce((acc, [key, value]) => {
                        const dtype = metadata.registers[key]?.dtype;
                        acc[key] = dtype ? DtypeMapper.formatValueForSQL(value, dtype) : value;
                        return acc;
                    }, {});

                    logger.info(`Logging data for device`, {
                        mac,
                        deviceTime: devTime,
                        format: 'legacy-object',
                        keys: Object.keys(formattedPayload).length
                    });

                    await this.dataService.logData(mac, parseInt(devTime), formattedPayload);

                    // Log the timestamp from the header
                    logDeviceData(mac, devTime, 'Data logged successfully', {
                        format: 'legacy',
                        timestamp: parseInt(devTime)
                    });
                }

                logger.info('Getting latest data timestamp', { mac });
                const dataTime = await this.dataService.getLatestDataTimestamp(mac);
                logger.info(`Latest data timestamp: ${dataTime}`, { mac, deviceTime: devTime });

                const response = `${serverTime},${dataTime},0`;
                logDeviceResponse(mac, devTime, 'Data processed successfully', response);
                return reply.code(200).send(response);
            }

            // Handle metadata update
            logger.info('Processing metadata update', { mac });
            const metadata = payload;

            logger.debug('Metadata update payload received', {
                mac,
                metadataKeys: Object.keys(metadata)
            });

            logger.info('Handling metadata update', { mac });
            const changes = await this.deviceService.handleMetadataUpdate(mac, metadata, this.dataService);
            logger.info(`Metadata changes processed`, {
                mac,
                changeType: changes.type,
                details: changes
            });

            const dataTime = await this.dataService.getLatestDataTimestamp(mac) || 0;
            const response = `${serverTime},${dataTime},0`;

            switch (changes.type) {
                case 'INCOMPATIBLE':
                    logger.warn('Incompatible metadata changes detected and applied', {
                        mac,
                        changes
                    });
                    return reply.code(200).send({
                        timestamp: response,
                        changes,
                        message: 'Metadata updates with incompatible changes, table recreated, old data destroyed'
                    });

                case 'COMPATIBLE':
                case 'BENIGN':
                    logger.info('Compatible or benign metadata changes applied', {
                        mac,
                        changeType: changes.type
                    });
                    return reply.code(200).send({
                        timestamp: response,
                        changes,
                        message: 'Metadata updated successfully'
                    });

                default:
                    logger.error(`Unknown change type encountered`, {
                        mac,
                        changeType: changes.type
                    });
                    throw new Error(`Unknown change type: ${changes.type}`);
            }
        } catch (error) {
            logDeviceError(
                request.headers['mac'],
                request.headers['dev-time'],
                'Device post error',
                error
            );

            return reply.code(500).send({
                error: 'Internal server error',
                // message: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    async getMetadataHistory(request, reply) {
        try {
            const { mac } = request.params;

            logger.info(`Retrieving metadata history`, { mac });

            const device = await this.deviceService.findDeviceByMac(mac);
            if (!device) {
                logger.warn(`Device not found when retrieving metadata history`, { mac });
                return reply.code(404).send({ error: 'Device not found' });
            }

            const history = await this.deviceService.getMetadataHistory(mac);
            logger.info(`Metadata history retrieved successfully`, {
                mac,
                historyLength: history.length
            });

            return reply.code(200).send(history);

        } catch (error) {
            logDeviceError(request.params.mac, null, 'Metadata history error', error);
            return reply.code(500).send({
                error: 'Failed to retrieve metadata history',
                message: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }
}
