import knex from 'knex';
import knexConfig from './knexfile.js';

async function checkDatabase() {
    const db = knex(knexConfig.development);
    
    try {
        console.log('=== Database Tables ===');
        
        // Check what tables exist
        const tables = await db.raw("SELECT name FROM sqlite_master WHERE type='table'");
        console.log('Existing tables:', tables.map(t => t.name));
        
        // Check migrations table
        try {
            const migrations = await db('knex_migrations').select('*');
            console.log('\n=== Migrations ===');
            migrations.forEach(m => {
                console.log(`${m.id}: ${m.name} - ${m.migration_time}`);
            });
        } catch (error) {
            console.log('No migrations table found');
        }
        
        // Try to create files table manually if it doesn't exist
        const hasFilesTable = tables.some(t => t.name === 'files');
        if (!hasFilesTable) {
            console.log('\n=== Creating files table manually ===');
            await db.schema.createTable('files', table => {
                table.string('mac_address').notNullable();
                table.string('relative_path').notNullable();
                table.integer('timestamp').notNullable();
                
                // Create unique index on mac_address and relative_path combined
                table.unique(['mac_address', 'relative_path']);
            });
            console.log('✓ Files table created successfully');
        } else {
            console.log('\n✓ Files table already exists');
        }
        
    } catch (error) {
        console.error('Database error:', error);
    } finally {
        await db.destroy();
    }
}

checkDatabase();
