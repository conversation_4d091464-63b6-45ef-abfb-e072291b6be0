{"name": "server", "version": "1.0.0", "type": "module", "main": "app_server.js", "scripts": {"prod-app": "cross-env NODE_ENV=production node ./src/app_server.js", "prod-device": "cross-env NODE_ENV=production node ./src/device_server.js", "dev-app": "cross-env NODE_ENV=development node ./src/app_server.js", "dev-device": "cross-env NODE_ENV=development node ./src/device_server.js", "migrate": "npx knex migrate:latest"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@fastify/auth": "^5.0.2", "@fastify/cors": "^11.0.0", "@fastify/jwt": "^9.0.4", "bcryptjs": "^3.0.2", "dotenv": "^16.4.7", "fastify": "^5.2.1", "fastify-plugin": "^5.0.1", "fs-extra": "^11.3.1", "knex": "^3.1.0", "nodemailer": "^6.10.0", "sqlite3": "^5.1.7", "winston": "^3.17.0"}, "devDependencies": {"cross-env": "^7.0.3"}}