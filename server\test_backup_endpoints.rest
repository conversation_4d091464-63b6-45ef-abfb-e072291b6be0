### Test Backup Endpoints

# First, test the /ls endpoint (should return empty array initially)
GET http://localhost:9001/api/device/ls
X-MAC-Address: 00:11:22:33:44:55

###

# Test upload endpoint with a simple text file
POST http://localhost:9001/api/device/upload?path=config/settings.json&timestamp=1704067200
X-MAC-Address: 00:11:22:33:44:55
Content-Type: application/gzip

< ./test_data/test_config.json.gz

###

# Test /ls endpoint again (should now show the uploaded file)
GET http://localhost:9001/api/device/ls
X-MAC-Address: 00:11:22:33:44:55

###

# Test upload with different file
POST http://localhost:9001/api/device/upload?path=logs/system.log&timestamp=1704070800
X-MAC-Address: 00:11:22:33:44:55
Content-Type: application/gzip

H4sIAAAAAAAAA0vOyS9NScwr0SsoyMwrziu1UoIB/QLfxOJUTSsFn8rk/NK0olRNAFD9iEtBAAAA

###

# Test /ls endpoint with multiple files
GET http://localhost:9001/api/device/ls
X-MAC-Address: 00:11:22:33:44:55

###

# Test with different MAC address (should return empty)
GET http://localhost:9001/api/device/ls
X-MAC-Address: 00:11:22:33:44:66

###

# Test error cases

# Missing MAC address header
GET http://localhost:9001/api/device/ls

###

# Missing path parameter
POST http://localhost:9001/api/device/upload?timestamp=1704067200
X-MAC-Address: 00:11:22:33:44:55
Content-Type: application/gzip

###

# Missing timestamp parameter
POST http://localhost:9001/api/device/upload?path=config/test.json
X-MAC-Address: 00:11:22:33:44:55
Content-Type: application/gzip

###

# Invalid timestamp parameter
POST http://localhost:9001/api/device/upload?path=config/test.json&timestamp=invalid
X-MAC-Address: 00:11:22:33:44:55
Content-Type: application/gzip


### Test upload with different file

