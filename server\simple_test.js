import zlib from 'zlib';

async function simpleTest() {
    const baseUrl = 'http://localhost:9001/api/device';
    const macAddress = '00:11:22:33:44:55';
    
    console.log('=== Simple Backup Endpoints Test ===\n');
    
    try {
        // Test 1: /ls endpoint (should be empty initially)
        console.log('1. Testing /ls endpoint...');
        const lsResponse1 = await fetch(`${baseUrl}/ls`, {
            headers: {
                'X-MAC-Address': macAddress
            }
        });
        
        if (lsResponse1.ok) {
            const files1 = await lsResponse1.json();
            console.log('✓ /ls response:', files1);
        } else {
            console.log('✗ /ls failed:', lsResponse1.status, await lsResponse1.text());
        }
        
        // Test 2: Create test content and upload
        console.log('\n2. Testing /upload endpoint...');
        
        // Create simple test content
        const testContent = JSON.stringify({
            "test": "data",
            "timestamp": Date.now()
        });
        
        // Gzip the content
        const gzippedContent = zlib.gzipSync(testContent);
        console.log('Created test content, gzipped size:', gzippedContent.length, 'bytes');
        
        const uploadResponse = await fetch(`${baseUrl}/upload?path=config/test.json&timestamp=1704067200`, {
            method: 'POST',
            headers: {
                'X-MAC-Address': macAddress,
                'Content-Type': 'application/gzip',
                'Content-Length': gzippedContent.length.toString()
            },
            body: gzippedContent
        });
        
        if (uploadResponse.ok) {
            const uploadResult = await uploadResponse.json();
            console.log('✓ Upload response:', uploadResult);
        } else {
            console.log('✗ Upload failed:', uploadResponse.status, await uploadResponse.text());
        }
        
        // Test 3: /ls endpoint again (should show uploaded file)
        console.log('\n3. Testing /ls endpoint again...');
        const lsResponse2 = await fetch(`${baseUrl}/ls`, {
            headers: {
                'X-MAC-Address': macAddress
            }
        });
        
        if (lsResponse2.ok) {
            const files2 = await lsResponse2.json();
            console.log('✓ /ls response:', files2);
        } else {
            console.log('✗ /ls failed:', lsResponse2.status, await lsResponse2.text());
        }
        
        console.log('\n=== Test completed ===');
        
    } catch (error) {
        console.error('Error during testing:', error);
    }
}

// Run test
simpleTest();
