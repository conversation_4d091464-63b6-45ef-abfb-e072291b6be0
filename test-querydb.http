### Query Data Route Test Cases

### VPS API 
POST http://pvapi1.netmeter.cloud/api/device/query-data
Content-Type: application/json

{
    "AA:BB:CC:DD:EE:FF":{}
}

### External API reference (for comparison)
GET https://geoscada.netmeter.cloud/api/trends?t0=1747540800&t1=1748145600&type=0&resolution=fine


### 11 
GET http://127.0.0.1:9001/api/device/query-data?device=AA:BB:CC:DD:EE:FF&stream=true&limit=1000


### fields 
POST http://pvapi1.netmeter.cloud/api/device/query-data
Content-Type: application/json

{
    "00:15:7E:3A:80:03":{
        "AnalogIn":["ts01", "ts02"],
        "Pump1":["MotorPower", "MotorInputVoltage"]
    }, 
    "AA:BB:CC:DD:EE:FF":{}
}

### Specific fields only
POST http://pvapi1.netmeter.cloud/api/device/query-data
Content-Type: application/json

{
    "devices": {
            "00:15:7E:3A:80:03": [],
            "AA:BB:CC:DD:EE:FF":{}
        },
    "limit":10
}

### Fields by index
POST http://127.0.0.1:9001/api/device/query-data
Content-Type: application/json

{
    "devices": {
        "AA:BB:CC:DD:EE:FF": ["temperature", "humidity", "pressure"]
    },
    "from": 1745952000,
    "to": 1745952400,
    "limit": 50
}
