import { <PERSON>ce<PERSON>ontroller } from '../../controllers/deviceController.js';
import { DeviceService } from '../../services/deviceService.js';
import { DataService } from '../../services/dataService.js';
import { StreamingService } from '../../services/streamingService.js';
import { TimeService } from '../../services/timeService.js';
import queryLogs from '../../utils/queryLogs.js';
import fs from 'fs-extra';
import zlib from 'zlib';
import path from 'path';
import { pipeline } from 'stream/promises';

export default async function deviceRoutes(fastify, options) {

    const knex = fastify.knex;

    // Add content type parser for gzipped content
    fastify.addContentTypeParser('application/gzip', { parseAs: 'buffer' }, (req, body, done) => {
        done(null, body);
    });

    const deviceService = new DeviceService(knex);
    const dataService = new DataService(knex, deviceService, fastify.log);
    const streamingService = new StreamingService(dataService, fastify.log);
    const timeService = new TimeService();

    const deviceController = new DeviceController(
        deviceService,
        dataService,
        timeService
    );

    // sending data (metadata or device data)
    fastify.post('/data', {
        handler: (request, reply) => {
            if (request.headers['dev-time']) {
                return deviceController.handleDevicePost(request, reply);
            } else {
                return deviceController.handleHandshake(request, reply);
            }
        }
    });

    // fetching metadata history>>> tested
    fastify.get('/meta-history/:mac', {
        handler: (request, reply) => deviceController.getMetadataHistory(request, reply)
    });

    // Query logs endpoint
    fastify.get('/logs', queryLogs);

    // Query device data from databases with streaming support
    fastify.post('/query-data', async (request, reply) => {
        try {
            const requestBody = request.body;
            const { from, to, limit = 1000, ...systems } = requestBody;

            // Validate systems object
            if (typeof systems !== 'object' || Object.keys(systems).length === 0) {
                return reply.code(400).send({ 
                    error: 'Invalid request',
                    message: 'At least one device MAC address is required'
                });
            }

            // Extract device list (MAC addresses) from object keys
            const deviceList = Object.keys(systems);

            // Parse parameters
            const parsedFrom = from && !isNaN(parseInt(from)) ? parseInt(from) : null;
            const parsedTo = to && !isNaN(parseInt(to)) ? parseInt(to) : null;
            const parsedLimit = limit && !isNaN(parseInt(limit)) ? parseInt(limit) : 1000;

            fastify.log.info(`POST /query-data - Devices: ${deviceList.length}, From: ${parsedFrom}, To: ${parsedTo}, Limit: ${parsedLimit}`);

            // Stream data from database
            await streamingService.streamMultipleDevices(deviceList, {
                deviceFields: systems, // Pass the systems object containing fields for each device
                from: parsedFrom,
                to: parsedTo,
                limit: parsedLimit,
            }, reply);

            return; // Response handled by streaming service

        } catch (error) {
            fastify.log.error('Error querying device data:', error);
            return reply.code(500).send({
                error: 'Failed to stream device data',
                message: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    });

    // backup file listing 
    fastify.get('/ls', async (request, reply) => {
        try {
            const macAddress = request.headers['x-mac-address'];
            if (!macAddress) {
                return reply.code(400).send({ error: 'Missing X-MAC-Address header' });
            }

            const backupRoot = process.env.BACKUP_ROOT || './data/backups';
            const sanitizedMacAddress = macAddress.replace(/:/g, '_');
            const deviceBackupPath = path.join(backupRoot, sanitizedMacAddress); 

            // Check if device backup directory exists
            if (!await fs.pathExists(deviceBackupPath)) {
                return reply.code(200).send([]);
            }

            const files = [];
            const walkDir = async (dir, basePath = '') => {
                const entries = await fs.readdir(dir, { withFileTypes: true });
                for (const entry of entries) {
                    const fullPath = path.join(dir, entry.name);
                    const relativePath = path.join(basePath, entry.name).replace(/\\/g, '/');
                    
                    if (entry.isDirectory()) {
                        await walkDir(fullPath, relativePath);
                    } else {
                        const stats = await fs.stat(fullPath);
                        files.push({
                            path: relativePath,
                            timestamp: Math.floor(stats.mtime.getTime() / 1000)
                        });
                    }
                }
            };

            await walkDir(deviceBackupPath);
            return reply.code(200).send(files);

        } catch (error) {
            fastify.log.error('Error listing backup files:', error);
            return reply.code(500).send({
                error: 'Failed to list backup files',
                message: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    });

    // backup file upload 
    fastify.post('/upload', async (request, reply) => {
        try {
            const macAddress = request.headers['x-mac-address'];
            const relativePath = request.query.path;
            const timestamp = parseInt(request.query.timestamp);
            const bufferSize = parseInt(request.query.bufferSize) || 64 * 1024; // Default 64KB

            // Validate params
            if (!macAddress) {
                return reply.code(400).send({ error: 'Missing X-MAC-Address header' });
            }
            if (!relativePath) {
                return reply.code(400).send({ error: 'Missing path query parameter' });
            }
            if (!timestamp || isNaN(timestamp)) {
                return reply.code(400).send({ error: 'Missing or invalid timestamp query parameter' });
            }

            // Setup paths
            const backupRoot = process.env.BACKUP_ROOT || './data/backups';
            const sanitizedMacAddress = macAddress.replace(/:/g, '_');
            const fullDestinationPath = path.join(backupRoot, sanitizedMacAddress, relativePath);

            await fs.ensureDir(path.dirname(fullDestinationPath));

            // Create streaming pipeline: request -> gunzip -> file
            const gunzipStream = zlib.createGunzip({ chunkSize: bufferSize });
            const writeStream = fs.createWriteStream(fullDestinationPath, { highWaterMark: bufferSize });

            // Check for empty content
            const contentLength = parseInt(request.headers['content-length']) || 0;
            if (contentLength === 0) {
                return reply.code(400).send({ error: 'No content provided for upload' });
            }

            await pipeline(request.raw, gunzipStream, writeStream);

            // Set file timestamp
            await fs.utimes(fullDestinationPath, new Date(timestamp * 1000), new Date(timestamp * 1000));

            return reply.code(200).send({
                message: 'File uploaded successfully',
                path: relativePath,
                timestamp: timestamp
            });

        } catch (error) {
            fastify.log.error('Error uploading backup file:', error);
            return reply.code(500).send({
                error: 'Failed to upload backup file',
                message: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    });
}


