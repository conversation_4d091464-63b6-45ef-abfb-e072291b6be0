import { <PERSON>ce<PERSON>ontroller } from '../../controllers/deviceController.js';
import { DeviceService } from '../../services/deviceService.js';
import { DataService } from '../../services/dataService.js';
import { StreamingService } from '../../services/streamingService.js';
import { TimeService } from '../../services/timeService.js';
import queryLogs from '../../utils/queryLogs.js';
import fs from 'fs-extra';
import zlib from 'zlib';
import path from 'path';
import { pipeline } from 'stream/promises';

export default async function deviceRoutes(fastify, options) {

    const knex = fastify.knex;

    // Add content type parser for gzipped content
    fastify.addContentTypeParser('application/gzip', { parseAs: 'buffer' }, (req, body, done) => {
        done(null, body);
    });

    const deviceService = new DeviceService(knex);
    const dataService = new DataService(knex, deviceService, fastify.log);
    const streamingService = new StreamingService(dataService, fastify.log);
    const timeService = new TimeService();

    const deviceController = new DeviceController(
        deviceService,
        dataService,
        timeService
    );

    // sending data (metadata or device data)
    fastify.post('/data', {
        handler: (request, reply) => {
            if (request.headers['dev-time']) {
                return deviceController.handleDevicePost(request, reply);
            } else {
                return deviceController.handleHandshake(request, reply);
            }
        }
    });

    // fetching metadata history>>> tested
    fastify.get('/meta-history/:mac', {
        handler: (request, reply) => deviceController.getMetadataHistory(request, reply)
    });

    // Query logs endpoint
    fastify.get('/logs', queryLogs);

    // Query device data from databases with streaming support
    fastify.post('/query-data', async (request, reply) => {
        try {
            const requestBody = request.body;
            const { from, to, limit = 1000, ...systems } = requestBody;

            // Validate systems object
            if (typeof systems !== 'object' || Object.keys(systems).length === 0) {
                return reply.code(400).send({ 
                    error: 'Invalid request',
                    message: 'At least one device MAC address is required'
                });
            }

            // Extract device list (MAC addresses) from object keys
            const deviceList = Object.keys(systems);

            // Parse parameters
            const parsedFrom = from && !isNaN(parseInt(from)) ? parseInt(from) : null;
            const parsedTo = to && !isNaN(parseInt(to)) ? parseInt(to) : null;
            const parsedLimit = limit && !isNaN(parseInt(limit)) ? parseInt(limit) : 1000;

            fastify.log.info(`POST /query-data - Devices: ${deviceList.length}, From: ${parsedFrom}, To: ${parsedTo}, Limit: ${parsedLimit}`);

            // Stream data from database
            await streamingService.streamMultipleDevices(deviceList, {
                deviceFields: systems, // Pass the systems object containing fields for each device
                from: parsedFrom,
                to: parsedTo,
                limit: parsedLimit,
            }, reply);

            return; // Response handled by streaming service

        } catch (error) {
            fastify.log.error('Error querying device data:', error);
            return reply.code(500).send({
                error: 'Failed to stream device data',
                message: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    });

    // List backup files for a device
    fastify.get('/ls', async (request, reply) => {
        try {
            const macAddress = request.headers['x-mac-address'];
            if (!macAddress) {
                return reply.code(400).send({ error: 'Missing X-MAC-Address header' });
            }

            // Query files table for this device
            const files = await knex('files')
                .select('relative_path as path', 'timestamp')
                .where('mac_address', macAddress)
                .orderBy('relative_path');

            return reply.code(200).send(files);

        } catch (error) {
            fastify.log.error('Error listing backup files:', error);
            return reply.code(500).send({
                error: 'Failed to list backup files',
                message: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    });

    // Upload backup file for a device
    fastify.post('/upload', async (request, reply) => {
        try {
            // Extract and validate parameters
            const macAddress = request.headers['x-mac-address'];
            const relativePath = request.query.path;
            const timestamp = parseInt(request.query.timestamp);

            if (!macAddress) {
                return reply.code(400).send({ error: 'Missing X-MAC-Address header' });
            }
            if (!relativePath) {
                return reply.code(400).send({ error: 'Missing path query parameter' });
            }
            if (!timestamp || isNaN(timestamp)) {
                return reply.code(400).send({ error: 'Missing or invalid timestamp query parameter' });
            }

            // Setup file paths
            const backupRoot = process.env.BACKUP_ROOT || './data/backups';
            const sanitizedMacAddress = macAddress.replace(/:/g, '_');
            const fullDestinationPath = path.join(backupRoot, sanitizedMacAddress, relativePath);

            // Ensure directory exists
            await fs.ensureDir(path.dirname(fullDestinationPath));

            // Get the gzipped content from request body (parsed as buffer)
            const gzippedContent = request.body;

            // Validate that we have content
            if (!gzippedContent || gzippedContent.length === 0) {
                return reply.code(400).send({ error: 'No content provided for upload' });
            }

            // Decompress the content
            const decompressedContent = zlib.gunzipSync(gzippedContent);

            // Write the decompressed content to file
            await fs.writeFile(fullDestinationPath, decompressedContent);

            // Set file modification timestamp
            await fs.utimes(fullDestinationPath, new Date(timestamp * 1000), new Date(timestamp * 1000));

            // Update database record
            await knex('files')
                .insert({
                    mac_address: macAddress,
                    relative_path: relativePath,
                    timestamp: timestamp
                })
                .onConflict(['mac_address', 'relative_path'])
                .merge(['timestamp']);

            return reply.code(200).send({
                message: 'File uploaded successfully',
                path: relativePath,
                timestamp: timestamp
            });

        } catch (error) {
            fastify.log.error('Error uploading backup file:', error);
            return reply.code(500).send({
                error: 'Failed to upload backup file',
                message: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    });
}


