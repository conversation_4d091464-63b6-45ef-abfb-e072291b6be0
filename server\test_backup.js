import fs from 'fs-extra';
import zlib from 'zlib';
import path from 'path';

// Test script to create gzipped test files and test backup endpoints

async function createTestFile() {
    const testContent = JSON.stringify({
        "device_id": "test-device",
        "settings": {
            "interval": 30,
            "enabled": true,
            "sensors": ["temperature", "humidity", "pressure"]
        },
        "timestamp": Date.now()
    }, null, 2);

    // Create gzipped content
    const gzippedContent = zlib.gzipSync(testContent);
    
    // Save to a test file
    await fs.ensureDir('./test_data');
    await fs.writeFile('./test_data/test_config.json.gz', gzippedContent);
    
    console.log('Test file created: ./test_data/test_config.json.gz');
    console.log('Original content length:', testContent.length);
    console.log('Gzipped content length:', gzippedContent.length);
    
    return gzippedContent;
}

async function testBackupEndpoints() {
    try {
        // Create test file
        const gzippedContent = await createTestFile();
        
        console.log('\n=== Testing Backup Endpoints ===');
        console.log('1. Start the device server: npm run dev-device');
        console.log('2. Use the REST client to test the endpoints in test_backup_endpoints.rest');
        console.log('3. Or use curl commands:');
        console.log('\n# Test /ls endpoint (should be empty initially):');
        console.log('curl -H "X-MAC-Address: 00:11:22:33:44:55" http://localhost:9001/api/device/ls');
        
        console.log('\n# Upload test file:');
        console.log('curl -X POST \\');
        console.log('  -H "X-MAC-Address: 00:11:22:33:44:55" \\');
        console.log('  -H "Content-Type: application/gzip" \\');
        console.log('  --data-binary @./test_data/test_config.json.gz \\');
        console.log('  "http://localhost:9001/api/device/upload?path=config/settings.json&timestamp=1704067200"');
        
        console.log('\n# Test /ls endpoint again (should show uploaded file):');
        console.log('curl -H "X-MAC-Address: 00:11:22:33:44:55" http://localhost:9001/api/device/ls');
        
    } catch (error) {
        console.error('Error creating test files:', error);
    }
}

// Run the test
testBackupEndpoints();
