import fs from 'fs-extra';

async function testEndpoints() {
    const baseUrl = 'http://localhost:9001/api/device';
    const macAddress = '00:11:22:33:44:55';
    
    console.log('=== Testing Backup Endpoints ===\n');
    
    try {
        // Test 1: /ls endpoint (should be empty initially)
        console.log('1. Testing /ls endpoint (should be empty initially)...');
        const lsResponse1 = await fetch(`${baseUrl}/ls`, {
            headers: {
                'X-MAC-Address': macAddress
            }
        });
        
        if (lsResponse1.ok) {
            const files1 = await lsResponse1.json();
            console.log('✓ /ls response:', files1);
        } else {
            console.log('✗ /ls failed:', lsResponse1.status, await lsResponse1.text());
        }
        
        // Test 2: Upload a file
        console.log('\n2. Testing /upload endpoint...');
        const gzippedContent = await fs.readFile('./server/test_data/test_config.json.gz');
        
        const uploadResponse = await fetch(`${baseUrl}/upload?path=config/settings.json&timestamp=1704067200`, {
            method: 'POST',
            headers: {
                'X-MAC-Address': macAddress,
                'Content-Type': 'application/gzip'
            },
            body: gzippedContent
        });
        
        if (uploadResponse.ok) {
            const uploadResult = await uploadResponse.json();
            console.log('✓ Upload response:', uploadResult);
        } else {
            console.log('✗ Upload failed:', uploadResponse.status, await uploadResponse.text());
        }
        
        // Test 3: /ls endpoint again (should show uploaded file)
        console.log('\n3. Testing /ls endpoint again (should show uploaded file)...');
        const lsResponse2 = await fetch(`${baseUrl}/ls`, {
            headers: {
                'X-MAC-Address': macAddress
            }
        });
        
        if (lsResponse2.ok) {
            const files2 = await lsResponse2.json();
            console.log('✓ /ls response:', files2);
        } else {
            console.log('✗ /ls failed:', lsResponse2.status, await lsResponse2.text());
        }
        
        // Test 4: Upload another file
        console.log('\n4. Testing upload of another file...');
        const uploadResponse2 = await fetch(`${baseUrl}/upload?path=logs/system.log&timestamp=1704070800`, {
            method: 'POST',
            headers: {
                'X-MAC-Address': macAddress,
                'Content-Type': 'application/gzip'
            },
            body: gzippedContent // Using same content for simplicity
        });
        
        if (uploadResponse2.ok) {
            const uploadResult2 = await uploadResponse2.json();
            console.log('✓ Upload response:', uploadResult2);
        } else {
            console.log('✗ Upload failed:', uploadResponse2.status, await uploadResponse2.text());
        }
        
        // Test 5: /ls endpoint with multiple files
        console.log('\n5. Testing /ls endpoint with multiple files...');
        const lsResponse3 = await fetch(`${baseUrl}/ls`, {
            headers: {
                'X-MAC-Address': macAddress
            }
        });
        
        if (lsResponse3.ok) {
            const files3 = await lsResponse3.json();
            console.log('✓ /ls response:', files3);
        } else {
            console.log('✗ /ls failed:', lsResponse3.status, await lsResponse3.text());
        }
        
        // Test 6: Different MAC address (should be empty)
        console.log('\n6. Testing /ls with different MAC address (should be empty)...');
        const lsResponse4 = await fetch(`${baseUrl}/ls`, {
            headers: {
                'X-MAC-Address': '00:11:22:33:44:66'
            }
        });
        
        if (lsResponse4.ok) {
            const files4 = await lsResponse4.json();
            console.log('✓ /ls response:', files4);
        } else {
            console.log('✗ /ls failed:', lsResponse4.status, await lsResponse4.text());
        }
        
        console.log('\n=== All tests completed ===');
        
    } catch (error) {
        console.error('Error during testing:', error);
    }
}

// Run tests
testEndpoints();
