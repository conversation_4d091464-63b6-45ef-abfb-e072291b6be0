### 1. Register a device with metadata (handshake)
POST http://pvapi1.netmeter.cloud/api/device/data
Content-Type: application/json

{
  "mac": "AA:BB:CC:DD:EE:FF",
  "model": "SensorHub-2000",
  "registers": [
    {
      "register": "temperature",
      "dtype": "f32",
      "units": "°C",
      "group": "Environment",
      "index": 0
    },
    {
      "register": "humidity",
      "dtype": "f32",
      "units": "%",
      "group": "Environment",
      "index": 1
    },
    {
      "register": "pressure",
      "dtype": "f32",
      "units": "hPa",
      "group": "Environment",
      "index": 2
    },
    {
      "register": "co2",
      "dtype": "i32",
      "units": "ppm",
      "group": "AirQuality",
      "index": 3
    },
    {
      "register": "door_status",
      "dtype": "bool",
      "group": "Security",
      "index": 4
    },
    {
      "register": "system_status_update_test_1",
      "dtype": "string",
      "group": "System",
      "index": 5
    }
  ],
  "label": "Office Environment Monitor",
  "description": "Monitors temperature, humidity, pressure, and air quality"
}

### 2. Send data payload to the device
POST http://127.0.0.1:9001/api/device/data
Content-Type: application/json
mac: AA:BB:CC:DD:EE:FF
dev-time: 1745952000

{
  "0": 26.0,
  "1": 45.2,
  "2": 1013.25,
  "3": 65,
  "4": true,
  "5": "normal"
}

### 3. Send another data payload with different values
POST http://127.0.0.1:9001/api/device/data
Content-Type: application/json
mac: AA:BB:CC:DD:EE:FF
dev-time: 1745952100

{
  "0": 26.1,
  "1": 46.5,
  "2": 1012.8,
  "3": 70,
  "4": false,
  "5": "warning"
}

### 5. Send data payload with the new register
POST http://127.0.0.1:9001/api/device/data
Content-Type: application/json
mac: AA:BB:CC:DD:EE:FF
dev-time: 1745952200

{
  "0": 25.8,
  "1": 44.9,
  "2": 1013.0,
  "4": true,
  "5": "normal",
  "6": 87.5,
}

### 6. Update metadata with a dtype change (change temperature from f32 to f64)
POST http://127.0.0.1:9001/api/device/data
Content-Type: application/json

{
  "mac": "AA:BB:CC:DD:EE:FF",
  "model": "SensorHub-2000",
  "label": "Office Environment Monitor",
  "description": "Monitors temperature, humidity, pressure, air quality, and battery",
  "registers": [
    {
      "register": "temperature",
      "dtype": "f64",
      "units": "°C",
      "group": "Environment",
      "index": 0
    },
    {
      "register": "humidity",
      "dtype": "f32",
      "units": "%",
      "group": "Environment",
      "index": 1
    },
    {
      "register": "pressure",
      "dtype": "f32",
      "units": "hPa",
      "group": "Environment",
      "index": 2
    },
    {
      "register": "co2",
      "dtype": "i32",
      "units": "ppm",
      "group": "AirQuality",
      "index": 3
    },
    {
      "register": "door_status",
      "dtype": "bool",
      "group": "Security",
      "index": 4
    },
    {
      "register": "system_status",
      "dtype": "string",
      "group": "System",
      "index": 5
    },
    {
      "register": "battery_level",
      "dtype": "f32",
      "units": "%",
      "group": "System",
      "index": 6
    }
  ],
  "trends": [
    {
      "title": "Temperature",
      "values": [
        {
          "device": "AA:BB:CC:DD:EE:FF",
          "register": "temperature"
        }
      ]
    }
  ]
}

### 7. Send data payload after dtype change
POST http://127.0.0.1:9001/api/device/data
Content-Type: application/json
mac: AA:BB:CC:DD:EE:FF
dev-time: 1745952300

[
  [1400435085, 36.25, 45.5, 1013.1, 66, true, "normal", 85.0],
  [1400435086, 36.25, 45.5, 1013.1, 66, false, "normal", 85.0]
]

### 8. Test with CSV format (multiple rows)
POST http://127.0.0.1:9001/api/device/data
Content-Type: text/csv
mac: AA:BB:CC:DD:EE:FF
dev-time: 1745952400

1400435090,25.9,45.5,1013.1,66,1,normal,85.0
1400435091,26.0,45.6,1013.0,67,0,warning,84.5
1400435092,26.0,45.7,1012.9,68,1,normal,84.0

### Testing with invalid data points 
POST http://127.0.0.1:9001/api/device/data
Content-Type: application/json
mac: AA:BB:CC:DD:EE:FF
dev-time: 1745952300

[
  [ 1400435005, 36.25, 45.5, 1013.1, 66, 1, "normal", 85.0],
  [ 1400435006, 36.25, 45.5, 1013.1, 660, 100, "normal", 85.0]
]

### Testing with more data points (valid) 
POST http://127.0.0.1:9001/api/device/data
Content-Type: application/json
mac: AA:BB:CC:DD:EE:FF
dev-time: 1745952300

[
  [ 1400435085, 36.25, 45.5, 1013.1, 66, true, "normal", 85.0],
  [ 1400435086, 36.25, 45.5, 1013.1, 660, false, "normal", 85.0, 5400, 2344, true]
]

### Testing with less data points (invalid) 
POST http://127.0.0.1:9001/api/device/data
Content-Type: application/json
mac: AA:BB:CC:DD:EE:FF
dev-time: 1745952300

[
  [ 1400435085, 36.25, 45.5, 1013.1, 66, true, "normal", 85.0],
  [ 1400439999, 36.25, 45.5, 66, true],
  [ 1400435086, 36.25, 45.5, 1013.1, 660, false, "normal", 85.0]
]
### 9. Get metadata history for the device
GET http://127.0.0.1:9001/api/device/meta-history/AA:BB:CC:DD:EE:FF

### 10. Test delay
GET https://httpbin.org/delay/1

