### Test Trends Array Storage Fix

### 1. Register a device with trends array
POST http://127.0.0.1:9001/api/device/data
Content-Type: application/json

{
    "mac": "AA:BB:CC:DD:EE:FF",
    "model": "Test-Device-With-Trends",
    "label": "Test Device",
    "description": "Testing trends array storage",
    "registers": [
        {
            "index": 1,
            "dtype": "F32",
            "group": "AnalogIn",
            "device": "AnalogIn",
            "register": "TEMP1",
            "units": "°C",
            "display": 2
        },
        {
            "index": 2,
            "dtype": "F32",
            "group": "AnalogIn",
            "device": "AnalogIn",
            "register": "TEMP2",
            "units": "°C",
            "display": 2
        }
    ],
    "trends": [
        {
            "title": "Temperature",
            "values": [
                {
                    "device": "AnalogIn",
                    "register": "TEMP1"
                },
                {
                    "device": "AnalogIn",
                    "register": "TEMP2"
                }
            ]
        }
    ]
}

### 2. Get metadata history to verify trends are stored
GET http://127.0.0.1:9001/api/device/meta-history/AA:BB:CC:DD:EE:FF

### 3. Update metadata with modified trends (should increment version)
POST http://127.0.0.1:9001/api/device/data
Content-Type: application/json

{
    "mac": "AA:BB:CC:DD:EE:FF",
    "model": "Test-Device-With-Trends",
    "label": "Test Device",
    "description": "Testing trends array storage",
    "registers": [
        {
            "index": 1,
            "dtype": "F32",
            "group": "AnalogIn",
            "device": "AnalogIn",
            "register": "TEMP1",
            "units": "°C",
            "display": 2
        },
        {
            "index": 2,
            "dtype": "F32",
            "group": "AnalogIn",
            "device": "AnalogIn",
            "register": "TEMP2",
            "units": "°C",
            "display": 2
        }
    ],
    "trends": [
        {
            "title": "Temperature Sensors",
            "values": [
                {
                    "device": "AnalogIn",
                    "register": "TEMP1"
                },
                {
                    "device": "AnalogIn",
                    "register": "TEMP2"
                }
            ]
        },
        {
            "title": "All Sensors",
            "values": [
                {
                    "device": "AnalogIn",
                    "register": "TEMP1"
                },
                {
                    "device": "AnalogIn",
                    "register": "TEMP2"
                }
            ]
        }
    ]
}

### 4. Get metadata history again to verify version increment
GET http://127.0.0.1:9001/api/device/meta-history/AA:BB:CC:DD:EE:FF
