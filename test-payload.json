{"mac": "AA:BB:CC:DD:EE:FF", "model": "Test-Device-With-Trends", "label": "Test Device", "description": "Testing trends array storage", "registers": [{"index": 1, "dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "TEMP1", "units": "°C", "display": 2}, {"index": 2, "dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "TEMP2", "units": "°C", "display": 2}], "trends": [{"title": "Temperature", "values": [{"device": "AnalogIn", "register": "TEMP1"}, {"device": "AnalogIn", "register": "TEMP2"}]}]}